// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { motion, AnimatePresence } from "framer-motion";
import { useEffect, useState } from "react";

import { cn } from "~/lib/utils";

import { Welcome } from "./welcome";

const researchQuestions = [
  "How many times taller is the Eiffel Tower than the tallest building in the world?",
  "How many years does an average Tesla battery last compared to a gasoline engine?",
  "How many liters of water are required to produce 1 kg of beef?",
  "How many times faster is the speed of light compared to the speed of sound?",
];

const kbQuestions = [
  "Build knowledge base on artificial intelligence trends",
  "Gather 30 articles about renewable energy technology",
  "Create knowledge base on cybersecurity best practices",
  "Collect knowledge about blockchain applications in finance",
];

export function ConversationStarter({
  className,
  onSend,
  chatMode = 'research',
}: {
  className?: string;
  onSend?: (message: string) => void;
  chatMode?: 'research' | 'kb_building';
}) {
  const [currentQuestions, setCurrentQuestions] = useState(researchQuestions);
  const [questionKey, setQuestionKey] = useState(0);

  useEffect(() => {
    // Update questions based on chat mode and trigger re-render
    const newQuestions = chatMode === 'kb_building' ? kbQuestions : researchQuestions;
    setCurrentQuestions(newQuestions);
    setQuestionKey(prev => prev + 1); // Force re-render with new key
  }, [chatMode]);

  return (
    <div className={cn("flex flex-col items-center", className)}>
      <div className="pointer-events-none fixed inset-0 flex items-center justify-center">
        <Welcome className="pointer-events-auto mb-15 w-[75%] -translate-y-24" />
      </div>
      <AnimatePresence mode="wait">
        <motion.ul
          key={questionKey}
          className="flex flex-wrap"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {currentQuestions.map((question: string, index: number) => (
            <motion.li
              key={`${questionKey}-${question}`}
              className="flex w-1/2 shrink-0 p-2 active:scale-105"
              style={{ transition: "all 0.2s ease-out" }}
              initial={{ opacity: 0, y: 24 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                duration: 0.2,
                delay: index * 0.1 + 0.1,
                ease: "easeOut",
              }}
            >
              <div
                className={cn(
                  "bg-card text-muted-foreground cursor-pointer rounded-2xl border px-4 py-4 opacity-75 transition-all duration-300 hover:opacity-100 hover:shadow-md",
                  // Add visual distinction for KB building questions
                  chatMode === 'kb_building'
                    ? "border-blue-200 hover:border-blue-300 hover:bg-blue-50/50 dark:border-blue-800 dark:hover:border-blue-700 dark:hover:bg-blue-950/50"
                    : "hover:border-gray-300 hover:bg-gray-50/50 dark:hover:border-gray-600 dark:hover:bg-gray-800/50"
                )}
                onClick={() => {
                  onSend?.(question);
                }}
              >
                <div className="flex items-start gap-2">
                  {/* Add icon for KB building questions */}
                  {chatMode === 'kb_building' && (
                    <span className="text-blue-500 text-sm">🗃️</span>
                  )}
                  {chatMode === 'research' && (
                    <span className="text-gray-500 text-sm">🔍</span>
                  )}
                  <span>{question}</span>
                </div>
              </div>
            </motion.li>
          ))}
        </motion.ul>
      </AnimatePresence>
    </div>
  );
}