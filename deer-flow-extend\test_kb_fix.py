#!/usr/bin/env python3
"""
Test script to verify the KB workflow fixes.
This script tests the specific functions that were causing the error.
"""

import sys
import os
from uuid import uuid4

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_convert_kb_message_to_event():
    """Test the _convert_kb_message_to_event function with different message types."""

    # Mock the _make_event function
    def _make_event(event_type, data):
        return f"event: {event_type}\ndata: {data}\n\n"

    # Recreate the fixed function logic here to test it
    def _convert_kb_message_to_event(message, thread_id: str):
        """Convert KB workflow messages to standard chat events"""

        # Handle case where message might be a string
        if isinstance(message, str):
            return _make_event("message_chunk", {
                "thread_id": thread_id,
                "agent": "kb_agent",
                "id": str(uuid4()),
                "role": "assistant",
                "content": message,
            })

        # Handle case where message is not a BaseMessage object
        if not hasattr(message, 'content'):
            return _make_event("message_chunk", {
                "thread_id": thread_id,
                "agent": "kb_agent",
                "id": str(uuid4()),
                "role": "assistant",
                "content": str(message),
            })

        # Handle regular messages
        return _make_event("message_chunk", {
            "thread_id": thread_id,
            "agent": getattr(message, 'name', 'kb_agent'),
            "id": getattr(message, 'id', str(uuid4())),
            "role": "assistant",
            "content": message.content,
        })

    thread_id = "test_thread"

    # Test 1: String message (this was causing the original error)
    print("Test 1: String message")
    try:
        result = _convert_kb_message_to_event("This is a string message", thread_id)
        print("✓ String message handled successfully")
        print(f"Result: {result}")
    except Exception as e:
        print(f"✗ String message failed: {e}")

    # Test 2: Object without content attribute
    print("\nTest 2: Object without content attribute")
    try:
        class MockObject:
            def __init__(self):
                self.name = "test"

        result = _convert_kb_message_to_event(MockObject(), thread_id)
        print("✓ Object without content handled successfully")
        print(f"Result: {result}")
    except Exception as e:
        print(f"✗ Object without content failed: {e}")

    # Test 3: Mock BaseMessage object
    print("\nTest 3: Mock BaseMessage object")
    try:
        class MockMessage:
            def __init__(self):
                self.content = "This is a mock message"
                self.name = "test_agent"
                self.id = str(uuid4())

        result = _convert_kb_message_to_event(MockMessage(), thread_id)
        print("✓ Mock BaseMessage handled successfully")
        print(f"Result: {result}")
    except Exception as e:
        print(f"✗ Mock BaseMessage failed: {e}")

def test_builder_timestamp_extraction():
    """Test the timestamp extraction fix in builder.py"""

    print("\nTest 4: Builder timestamp extraction")

    # Test with different message types
    test_cases = [
        # Case 1: List with message object
        {
            "messages": [
                type('MockMessage', (), {'content': 'Test message content'})()
            ]
        },
        # Case 2: List with string
        {
            "messages": ["String message"]
        },
        # Case 3: List with other object
        {
            "messages": [{"type": "dict", "data": "test"}]
        },
        # Case 4: Empty list
        {
            "messages": []
        }
    ]

    for i, node_output in enumerate(test_cases, 1):
        try:
            # Simulate the fixed timestamp extraction logic
            timestamp = ""
            messages = node_output.get("messages", [])
            if messages:
                last_message = messages[-1]
                if hasattr(last_message, 'content'):
                    timestamp = last_message.content
                elif isinstance(last_message, str):
                    timestamp = last_message
                else:
                    timestamp = str(last_message)

            print(f"✓ Case {i}: Timestamp extracted successfully: '{timestamp}'")
        except Exception as e:
            print(f"✗ Case {i}: Timestamp extraction failed: {e}")

if __name__ == "__main__":
    print("Testing KB workflow fixes...")
    print("=" * 50)

    test_convert_kb_message_to_event()
    test_builder_timestamp_extraction()

    print("\n" + "=" * 50)
    print("All tests completed!")
